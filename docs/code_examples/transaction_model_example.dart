import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

/// A category for classifying transactions.
///
/// Categories help organize transactions into logical groups for analysis
/// and reporting purposes. Each category has a name, color, and icon.
class Category {
  /// Unique identifier for the category.
  final String id;

  /// Display name of the category.
  final String name;

  /// Color used to represent this category in the UI.
  final Color color;

  /// Icon used to represent this category in the UI.
  final IconData icon;

  /// Creates a new category.
  ///
  /// [id] will be automatically generated if not provided.
  /// [name] is required and must not be empty.
  /// [color] defaults to grey if not specified.
  /// [icon] defaults to a generic icon if not specified.
  Category({
    String? id,
    required this.name,
    this.color = Colors.grey,
    this.icon = Icons.category,
  }) : id = id ?? const Uuid().v4();

  /// Creates a copy of this category with specified attributes replaced with new values.
  Category copyWith({
    String? name,
    Color? color,
    IconData? icon,
  }) {
    return Category(
      id: id,
      name: name ?? this.name,
      color: color ?? this.color,
      icon: icon ?? this.icon,
    );
  }
}

/// Represents a financial transaction in the application.
///
/// A transaction records a financial event such as an expense or income.
/// It includes details such as amount, date, category, and description.
class Transaction {
  /// Unique identifier for the transaction.
  final String id;

  /// The monetary amount of the transaction.
  ///
  /// Positive values represent income, negative values represent expenses.
  final double amount;

  /// The date when the transaction occurred.
  final DateTime date;

  /// The category this transaction belongs to.
  final Category category;

  /// A text description of the transaction.
  final String description;

  /// Optional path to an attached image (e.g., receipt).
  final String? imagePath;

  /// Creates a new transaction.
  ///
  /// [id] will be automatically generated if not provided.
  /// [amount] is required and can be positive (income) or negative (expense).
  /// [date] defaults to the current date if not specified.
  /// [category] is required and must be a valid Category object.
  /// [description] is required and should describe the transaction.
  /// [imagePath] is optional and points to an image file if provided.
  Transaction({
    String? id,
    required this.amount,
    DateTime? date,
    required this.category,
    required this.description,
    this.imagePath,
  })  : id = id ?? const Uuid().v4(),
        date = date ?? DateTime.now();

  /// Creates a copy of this transaction with specified attributes replaced with new values.
  Transaction copyWith({
    double? amount,
    DateTime? date,
    Category? category,
    String? description,
    String? imagePath,
  }) {
    return Transaction(
      id: id,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      category: category ?? this.category,
      description: description ?? this.description,
      imagePath: imagePath ?? this.imagePath,
    );
  }

  /// Returns true if this transaction represents an expense (negative amount).
  bool get isExpense => amount < 0;

  /// Returns true if this transaction represents income (positive amount).
  bool get isIncome => amount > 0;

  /// Returns the absolute value of the transaction amount.
  double get absoluteAmount => amount.abs();
}

/// Manages transaction data and provides methods for transaction operations.
///
/// This provider serves as the central store for all transaction data in the app.
/// It provides methods to add, update, and remove transactions, as well as
/// query transactions based on various criteria.
class TransactionProvider extends ChangeNotifier {
  /// Service for persisting transaction data.
  final StorageService _storageService;

  /// Internal list of all transactions.
  List<Transaction> _transactions = [];

  /// Default categories available in the app.
  List<Category> _categories = [];

  /// Creates a new TransactionProvider with the specified storage service.
  ///
  /// Initializes transaction and category data from storage if available.
  TransactionProvider(this._storageService) {
    _loadData();
  }

  /// Loads transaction and category data from persistent storage.
  ///
  /// This is called during initialization to restore saved data.
  Future<void> _loadData() async {
    _transactions = await _storageService.getTransactions();
    _categories = await _storageService.getCategories();
    notifyListeners();
  }

  /// Returns an unmodifiable list of all transactions.
  List<Transaction> get transactions => List.unmodifiable(_transactions);

  /// Returns an unmodifiable list of all categories.
  List<Category> get categories => List.unmodifiable(_categories);

  /// Adds a new transaction to the data store.
  ///
  /// [transaction] must be a valid Transaction object.
  /// Returns the added transaction with its assigned ID.
  Future<Transaction> addTransaction(Transaction transaction) async {
    _transactions.add(transaction);
    await _storageService.saveTransactions(_transactions);
    notifyListeners();
    return transaction;
  }

  /// Updates an existing transaction.
  ///
  /// [transaction] must have an ID that matches an existing transaction.
  /// Returns true if the update was successful, false otherwise.
  Future<bool> updateTransaction(Transaction transaction) async {
    final index = _transactions.indexWhere((t) => t.id == transaction.id);
    if (index < 0) return false;

    _transactions[index] = transaction;
    await _storageService.saveTransactions(_transactions);
    notifyListeners();
    return true;
  }

  /// Removes a transaction by its ID.
  ///
  /// [id] must be a valid transaction ID.
  /// Returns true if the transaction was removed, false if no matching transaction was found.
  Future<bool> removeTransaction(String id) async {
    final initialLength = _transactions.length;
    _transactions.removeWhere((t) => t.id == id);
    final removed = initialLength > _transactions.length;
    
    if (removed) {
      await _storageService.saveTransactions(_transactions);
      notifyListeners();
      return true;
    }
    return false;
  }

  /// Returns transactions for the specified time period.
  ///
  /// [startDate] is the beginning of the period.
  /// [endDate] is the end of the period.
  /// Returns a list of transactions within the date range (inclusive).
  List<Transaction> getTransactionsForPeriod(DateTime startDate, DateTime endDate) {
    return _transactions.where((t) {
      return t.date.isAfter(startDate.subtract(const Duration(days: 1))) && 
             t.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// Returns transactions belonging to the specified category.
  ///
  /// [categoryId] is the ID of the category to filter by.
  /// Returns a list of transactions in the specified category.
  List<Transaction> getTransactionsByCategory(String categoryId) {
    return _transactions.where((t) => t.category.id == categoryId).toList();
  }

  /// Adds a new category to the data store.
  ///
  /// [category] must be a valid Category object.
  /// Returns the added category with its assigned ID.
  Future<Category> addCategory(Category category) async {
    _categories.add(category);
    await _storageService.saveCategories(_categories);
    notifyListeners();
    return category;
  }

  /// Updates an existing category.
  ///
  /// [category] must have an ID that matches an existing category.
  /// Returns true if the update was successful, false otherwise.
  Future<bool> updateCategory(Category category) async {
    final index = _categories.indexWhere((c) => c.id == category.id);
    if (index < 0) return false;

    _categories[index] = category;
    await _storageService.saveCategories(_categories);
    notifyListeners();
    return true;
  }

  /// Removes a category by its ID.
  ///
  /// [id] must be a valid category ID.
  /// Returns true if the category was removed, false if no matching category was found.
  /// Note: This does not remove or update transactions that reference this category.
  Future<bool> removeCategory(String id) async {
    final initialLength = _categories.length;
    _categories.removeWhere((c) => c.id == id);
    final removed = initialLength > _categories.length;
    
    if (removed) {
      await _storageService.saveCategories(_categories);
      notifyListeners();
      return true;
    }
    return false;
  }
}

/// A mock class representing the storage service.
/// This is just for the example and would be implemented in a real service.
class StorageService {
  Future<List<Transaction>> getTransactions() async {
    // Implementation would retrieve data from storage
    return [];
  }

  Future<List<Category>> getCategories() async {
    // Implementation would retrieve data from storage
    return [];
  }

  Future<void> saveTransactions(List<Transaction> transactions) async {
    // Implementation would save data to storage
  }

  Future<void> saveCategories(List<Category> categories) async {
    // Implementation would save data to storage
  }
} 