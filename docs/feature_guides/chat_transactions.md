# Chat-Based Transaction Entry

This document explains the chat-based transaction entry feature, which is a core functionality of the Money Lover Chat application.

## Overview

The chat interface provides an intuitive, conversational way for users to enter financial transactions. Instead of filling out forms, users can type natural language statements like "Spent $25 on lunch today" or "Paid $50 for gas yesterday," and the app will parse these into structured transaction data.

## User Flow

```mermaid
graph TD
    A[User opens Chat Screen] --> B[User types transaction text]
    B --> C[App parses text]
    C --> D{Valid transaction?}
    D -->|Yes| E[Display transaction preview]
    D -->|No| F[Request clarification]
    E --> G[User confirms]
    G --> H[Transaction saved]
    F --> B
```

## Components Involved

### UI Components
- `ChatScreen`: Main interface for the chat interaction
- `MessageBubble`: Displays user and system messages
- `TransactionPreview`: Shows a preview of the parsed transaction

### Business Logic
- `TransactionProvider`: Manages transaction data
- `TransactionParserService`: Parses natural language into transaction data

### Data Storage
- `StorageService`: Persists transaction data

## Implementation Details

### Natural Language Parsing

The text parsing logic in `TransactionParserService` follows these steps:

1. **Extract amount**: Identify currency symbols and numbers
2. **Identify date**: Look for date indicators (today, yesterday, specific dates)
3. **Determine category**: Identify spending category based on keywords
4. **Detect transaction type**: Determine if it's income or expense

```mermaid
sequenceDiagram
    participant CS as ChatScreen
    participant TPS as TransactionParserService
    participant TP as TransactionProvider
    
    CS->>TPS: parseTransaction("Spent $25 on lunch today")
    Note over TPS: Extract amount: $25
    Note over TPS: Identify date: today
    Note over TPS: Determine category: Food
    Note over TPS: Detect type: Expense
    TPS->>CS: Transaction object
    CS->>CS: Display preview
    CS->>TP: addTransaction(transaction)
```

### Media Attachments

Users can add media attachments to transactions as supporting documentation:

1. **Images**: Receipts, invoices (via `ImageUpload`)
2. **Audio**: Voice notes explaining transactions (via `AudioRecorder`)
3. **Video**: Video documentation (via `VideoRecorder`)
4. **Files**: PDF statements, etc. (via `FileUpload`)

## Code Example

Here's how the transaction parsing is typically implemented:

```dart
// In TransactionParserService
Transaction parseTransaction(String text) {
  // Extract amount using regex
  final amountMatch = RegExp(r'\$\d+(\.\d{1,2})?').firstMatch(text);
  final amount = amountMatch != null
      ? double.parse(amountMatch.group(0)!.replaceAll('\$', ''))
      : 0.0;
  
  // Determine date
  final DateTime date = _extractDate(text);
  
  // Determine category based on keywords
  final category = _determineCategory(text);
  
  // Determine if income or expense
  final isExpense = _isExpenseTransaction(text);
  
  return Transaction(
    id: const Uuid().v4(),
    amount: isExpense ? -amount : amount,
    date: date,
    category: category,
    description: text,
  );
}
```

## User Experience Considerations

1. **Feedback Loop**: The app should provide immediate feedback about parsed transactions
2. **Error Handling**: Clear guidance when parsing fails
3. **Learning**: The system should improve category suggestions based on past transactions
4. **Flexibility**: Support various ways of expressing the same transaction

## Future Enhancements

1. **Machine Learning**: Improve parsing accuracy over time with ML
2. **Voice Input**: Add support for voice-to-text transaction entry
3. **Smart Suggestions**: Predict categories and amounts based on user history
4. **Receipt Scanning**: Extract transaction details from photographs of receipts

## Testing Strategy

1. **Unit Tests**: Test the parsing logic with various input formats
2. **Integration Tests**: Verify the flow from chat input to stored transaction
3. **User Testing**: Validate parsing accuracy with real user inputs 