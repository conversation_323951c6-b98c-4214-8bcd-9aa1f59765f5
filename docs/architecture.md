# Architecture Documentation

This document describes the architecture of the Money Lover Chat application, including its components, data flow, and key design patterns.

## Architectural Overview

The application follows a layered architecture pattern with Provider for state management:

```mermaid
graph TD
    UI[UI Layer - Screens & Widgets]
    BL[Business Logic Layer - Providers]
    SL[Service Layer]
    DL[Data Layer - Models & Storage]
    
    UI --> BL
    BL --> SL
    SL --> DL
    BL --> DL
```

## Component Diagram

The major components of the application and their interactions:

```mermaid
graph TD
    subgraph "Presentation Layer"
        CS[Categories Screen]
        ChS[Chat Screen]
        SS[Settings Screen]
        StS[Statistics Screen]
        AN[App Navigation]
    end
    
    subgraph "Business Logic Layer"
        TP[Transaction Provider]
        ThP[Theme Provider]
    end
    
    subgraph "Service Layer"
        STS[Storage Service]
        TPS[Transaction Parser Service]
        AR[Audio Recorder]
        FU[File Upload]
        IU[Image Upload]
        VR[Video Recorder]
    end
    
    subgraph "Data Layer"
        TM[Transaction Model]
        CM[Category Model]
        SP[Shared Preferences]
    end
    
    CS --> TP
    ChS --> TP
    ChS --> TPS
    ChS --> AR
    ChS --> FU
    ChS --> IU
    ChS --> VR
    SS --> ThP
    StS --> TP
    
    TP --> STS
    TP --> TM
    TP --> CM
    ThP --> SP
    
    STS --> SP
    TPS --> TM
```

## State Management

The application uses Provider pattern for state management. This provides a simple and efficient way to manage and propagate state changes throughout the application.

```mermaid
graph TD
    subgraph "Widget Tree"
        RW[Root Widget - MultiProvider]
        ML[MoneyLoverChatApp]
        AN[AppNavigation]
        SC[Screen Components]
    end
    
    subgraph "Providers"
        TP[TransactionProvider]
        ThP[ThemeProvider]
    end
    
    subgraph "Data Sources"
        STS[Storage Service]
        SP[Shared Preferences]
    end
    
    RW --> TP
    RW --> ThP
    RW --> ML
    ML --> AN
    AN --> SC
    
    SC -- Consumer --> TP
    SC -- Consumer --> ThP
    
    TP --> STS
    STS --> SP
    ThP --> SP
```

## Data Flow - Transaction Processing

The sequence of operations when a user enters a transaction via chat:

```mermaid
sequenceDiagram
    participant U as User
    participant CS as Chat Screen
    participant TPS as Transaction Parser Service
    participant TP as Transaction Provider
    participant STS as Storage Service
    
    U->>CS: Enter transaction text
    CS->>TPS: Parse text
    TPS->>CS: Return Transaction object
    CS->>TP: Add transaction
    TP->>STS: Save transaction
    STS-->>TP: Confirm save
    TP-->>CS: Update state
    CS-->>U: Display confirmation
```

## Data Flow - Theme Change

The sequence of operations when a user changes the app theme:

```mermaid
sequenceDiagram
    participant U as User
    participant SS as Settings Screen
    participant ThP as Theme Provider
    participant SP as Shared Preferences
    
    U->>SS: Change theme
    SS->>ThP: Update theme
    ThP->>SP: Save preference
    SP-->>ThP: Confirm save
    ThP-->>SS: Notify change (via Provider)
    SS-->>U: Display updated theme
```

## File Structure and Responsibility

| Component | Primary File(s) | Responsibility |
|-----------|-----------------|----------------|
| UI Layer | `screens/*.dart` | User interface and interaction |
| Navigation | `navigation/app_navigation.dart` | Screen routing and navigation |
| State Management | `models/transaction_model.dart`, `theme.dart` | App-wide state management |
| Services | `services/*.dart`, utility files | Business logic implementation |
| Data Models | `models/transaction_model.dart` | Data structure definitions |
| Storage | `services/storage_service.dart` | Data persistence |

## Design Patterns

- **Provider Pattern**: Used for state management across the application
- **Repository Pattern**: Used for data access abstraction in storage service
- **Service Layer Pattern**: Used to separate business logic from UI components
- **Observer Pattern**: Implemented via Provider for reactive UI updates 