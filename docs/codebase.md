# Codebase Structure

This document provides a detailed overview of the Money Lover Chat application codebase organization.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers and services
- **Data Layer**: Models and storage services

## Directory Structure

### Root Level

```
money_lover_chat/
├── android/          # Android-specific configuration
├── ios/              # iOS-specific configuration
├── lib/              # Main Dart code
├── pubspec.yaml      # Dependencies and project configuration
├── pubspec.lock      # Lock file with exact dependency versions
└── web/              # Web-specific configuration
```

### Main Application Code (lib/)

```
lib/
├── models/           # Data models
│   └── transaction_model.dart  # Transaction data models and providers
├── services/         # Business logic and data services
│   ├── storage_service.dart           # Local storage handling
│   └── transaction_parser_service.dart # Transaction parsing from text
├── screens/          # UI screens
│   ├── categories_screen.dart  # Categories management
│   ├── chat_screen.dart        # Chat interface for transaction entry
│   ├── settings_screen.dart    # App settings
│   └── statistics_screen.dart  # Financial statistics and charts
├── navigation/       # Navigation logic
│   └── app_navigation.dart     # Main navigation
├── main.dart         # Application entry point
├── theme.dart        # App theme configuration
├── audio_recorder.dart  # Audio recording functionality
├── file_upload.dart     # File upload functionality
├── image_upload.dart    # Image upload functionality
└── video_recorder.dart  # Video recording functionality
```

## Key Components

### Models

Located in `lib/models/`, these define the data structures used throughout the app.

- **transaction_model.dart**: Contains:
  - `Transaction` - Core data model for financial transactions
  - `Category` - Transaction category model
  - `TransactionProvider` - State management for transactions using Provider

### Services

Located in `lib/services/`, these handle the business logic and data operations.

- **storage_service.dart**: Manages local storage using SharedPreferences
- **transaction_parser_service.dart**: Natural language processing for converting text to transactions

### Screens

Located in `lib/screens/`, these are the main UI components of the app.

- **categories_screen.dart**: Manages transaction categories
- **chat_screen.dart**: Chat interface for entering transactions naturally
- **settings_screen.dart**: App configuration and preferences
- **statistics_screen.dart**: Financial data visualization and analysis

### Navigation

Located in `lib/navigation/`, handles the app's routing system.

- **app_navigation.dart**: Main navigation controller

### Utilities

Various utility files in the root of `lib/`:

- **theme.dart**: Theme configuration and ThemeProvider
- **audio_recorder.dart**: Audio recording functionality
- **file_upload.dart**: File upload utilities
- **image_upload.dart**: Image upload utilities
- **video_recorder.dart**: Video recording functionality

## State Management

The application uses Provider for state management. Main providers include:

- `TransactionProvider`: Manages transaction data and operations
- `ThemeProvider`: Manages app theme settings

## Dependencies

Key dependencies include:

- `provider`: State management
- `shared_preferences`: Local storage
- `fl_chart`: Data visualization
- `supabase_flutter`: Backend database connectivity
- `assets_audio_player`: Audio playback
- `record`: Audio recording
- `image_picker` & `file_picker`: Media selection
- `path_provider`: File system access
- `google_fonts`: Custom typography 