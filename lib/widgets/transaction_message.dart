import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/transaction_model.dart';

class TransactionMessage extends StatelessWidget {
  final Transaction transaction;
  final Category category;
  final Function(Transaction) onEdit;
  final Function(String) onDelete;

  const TransactionMessage({
    Key? key,
    required this.transaction,
    required this.category,
    required this.onEdit,
    required this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.brightness == Brightness.light 
            ? Colors.white 
            : Colors.grey.shade900,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Transaction header with category and amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    category.icon,
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    category.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: theme.brightness == Brightness.light 
                          ? Colors.black87 
                          : Colors.white,
                    ),
                  ),
                ],
              ),
              Text(
                provider.formatCurrency(transaction.amount),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _getTransactionColor(transaction.type, theme),
                ),
              ),
            ],
          ),
          
          // Transaction description if available
          if (transaction.description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                transaction.description,
                style: TextStyle(
                  fontSize: 14,
                  color: theme.brightness == Brightness.light 
                      ? Colors.black54 
                      : Colors.grey.shade300,
                ),
              ),
            ),
          
          // Transaction date
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              _formatDate(transaction.date),
              style: TextStyle(
                fontSize: 12,
                color: theme.brightness == Brightness.light 
                    ? Colors.black45 
                    : Colors.grey.shade400,
              ),
            ),
          ),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Edit button
                TextButton.icon(
                  onPressed: () => onEdit(transaction),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    minimumSize: const Size(0, 30),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
                const SizedBox(width: 8),
                // Delete button
                TextButton.icon(
                  onPressed: () => onDelete(transaction.id),
                  icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                  label: const Text('Delete', style: TextStyle(color: Colors.red)),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    minimumSize: const Size(0, 30),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getTransactionColor(TransactionType type, ThemeData theme) {
    switch (type) {
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.income:
        return Colors.green;
      case TransactionType.loan:
        return Colors.orange;
      default:
        return theme.colorScheme.primary;
    }
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    final hours = dateTime.hour.toString().padLeft(2, '0');
    final minutes = dateTime.minute.toString().padLeft(2, '0');
    final time = '$hours:$minutes';
    
    if (date == today) {
      return 'Today, $time';
    } else if (date == today.subtract(const Duration(days: 1))) {
      return 'Yesterday, $time';
    } else {
      final month = dateTime.month.toString().padLeft(2, '0');
      final day = dateTime.day.toString().padLeft(2, '0');
      return '$day/$month, $time';
    }
  }
} 