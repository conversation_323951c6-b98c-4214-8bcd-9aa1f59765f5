import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:flutter/rendering.dart';

import '../models/transaction_model.dart';
import '../widgets/transaction_edit_dialog.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedPeriod = 'month'; // 'week', 'month', 'year', 'custom'
  bool _showTransactionList = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setPeriod(String period) {
    setState(() {
      _selectedPeriod = period;
      final now = DateTime.now();
      
      switch (period) {
        case 'week':
          _startDate = now.subtract(const Duration(days: 7));
          _endDate = now;
          break;
        case 'month':
          _startDate = DateTime(now.year, now.month - 1, now.day);
          _endDate = now;
          break;
        case 'year':
          _startDate = DateTime(now.year - 1, now.month, now.day);
          _endDate = now;
          break;
        case 'custom':
          // Don't change dates, will be set by date picker
          break;
      }
      
      // Restart animation
      _animationController.reset();
      _animationController.forward();
    });
  }

  Future<void> _selectDateRange(BuildContext context) async {
    DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      saveText: 'Apply',
    );
    
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
        _selectedPeriod = 'custom';
        
        // Restart animation
        _animationController.reset();
        _animationController.forward();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to properly handle keep alive
    super.build(context);
    
    final provider = Provider.of<TransactionProvider>(context);
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    
    // Filter transactions by date range
    final transactions = provider.getTransactionsInDateRange(_startDate, _endDate);
    
    // Calculate totals
    final totalIncome = provider.getTotalByTypeInRange(TransactionType.income, _startDate, _endDate);
    final totalExpense = provider.getTotalByTypeInRange(TransactionType.expense, _startDate, _endDate);
    final totalLoan = provider.getTotalByTypeInRange(TransactionType.loan, _startDate, _endDate);
    final balance = totalIncome - totalExpense;
    
    // Get spending by category for pie chart
    final categorySpending = provider.getSpendingByCategory(_startDate, _endDate);
    
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.bar_chart),
            SizedBox(width: 8),
            Text('Statistics'),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(_showTransactionList ? Icons.list : Icons.bar_chart),
            onPressed: () {
              setState(() {
                _showTransactionList = !_showTransactionList;
              });
            },
            tooltip: _showTransactionList ? 'Show Charts' : 'Show Transactions',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () => _selectDateRange(context),
            tooltip: 'Select Date Range',
          ),
        ],
      ),
      body: Column(
        children: [
          // Date filter chips
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildFilterChip('week', 'Week'),
                _buildFilterChip('month', 'Month'),
                _buildFilterChip('year', 'Year'),
                _buildFilterChip('custom', 'Custom'),
                if (_selectedPeriod == 'custom')
                  Chip(
                    label: Text(
                      '${DateFormat('MMM d').format(_startDate)} - ${DateFormat('MMM d').format(_endDate)}',
                      style: TextStyle(
                        color: theme.colorScheme.onPrimary,
                        fontSize: 12,
                      ),
                    ),
                    backgroundColor: theme.colorScheme.primary,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
              ],
            ),
          ),
          
          // Summary Cards
          FadeTransition(
            opacity: _fadeAnimation,
            child: SizedBox(
              height: 100,
              child: ListView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                children: [
                  _buildSummaryCard('Balance', balance, Colors.blue, Icons.account_balance_wallet),
                  _buildSummaryCard('Income', totalIncome, Colors.green, Icons.arrow_downward),
                  _buildSummaryCard('Expenses', totalExpense, Colors.red, Icons.arrow_upward),
                  _buildSummaryCard('Loans', totalLoan, Colors.orange, Icons.compare_arrows),
                ],
              ),
            ),
          ),
          
          // Main content - either charts or transaction list
          Expanded(
            child: _showTransactionList
                ? _buildTransactionList(transactions, provider)
                : _buildCharts(categorySpending, provider, size, theme),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedPeriod == value;
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: ChoiceChip(
        label: Text(
          label,
          style: TextStyle(
            color: isSelected 
                ? theme.colorScheme.onPrimary 
                : theme.colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedColor: theme.colorScheme.primary,
        backgroundColor: theme.colorScheme.surface,
        onSelected: (selected) {
          if (selected) {
            _setPeriod(value);
          }
        },
      ),
    );
  }

  Widget _buildSummaryCard(String title, double amount, Color color, IconData icon) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final formattedAmount = provider.formatCurrency(amount);
    
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 140,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [color.withOpacity(0.7), color.withOpacity(0.4)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  title,
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
            Text(
              formattedAmount,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCharts(Map<String, double> categorySpending, TransactionProvider provider, Size size, ThemeData theme) {
    if (categorySpending.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart_outlined,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No data for selected period',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.withOpacity(0.8),
              ),
            ),
          ],
        ),
      );
    }
    
    // Convert the map to a list for the pie chart
    final pieData = categorySpending.entries.map((entry) {
      final category = provider.getCategoryById(entry.key);
      return MapEntry(
        category?.name ?? 'Other',
        entry.value,
      );
    }).toList();
    
    // Sort by amount (descending)
    pieData.sort((a, b) => b.value.compareTo(a.value));
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Spending by Category',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 24),
          Center(
            child: SizedBox(
              height: size.width * 0.7,
              width: size.width * 0.7,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return PieChart(
                          PieChartData(
                            sections: _buildPieChartSections(pieData, provider),
                            centerSpaceRadius: 40,
                            sectionsSpace: 2,
                            startDegreeOffset: -90,
                            pieTouchData: PieTouchData(enabled: true),
                          ),
                          swapAnimationDuration: const Duration(milliseconds: 800),
                          swapAnimationCurve: Curves.easeInOutQuint,
                        );
                      },
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Total',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          provider.formatCurrency(
                            categorySpending.values.fold(0, (sum, value) => sum + value),
                          ),
                          style: TextStyle(
                            color: theme.colorScheme.onSurface,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 32),
          Text(
            'Category Breakdown',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ...pieData.map((entry) => _buildCategoryLegendItem(entry.key, entry.value, provider)),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(List<MapEntry<String, double>> data, TransactionProvider provider) {
    final total = data.fold(0.0, (sum, item) => sum + item.value);
    final colors = [
      const Color(0xFF3F51B5),
      const Color(0xFF2196F3),
      const Color(0xFF03A9F4),
      const Color(0xFF00BCD4),
      const Color(0xFF009688),
      const Color(0xFF4CAF50),
      const Color(0xFF8BC34A),
      const Color(0xFFCDDC39),
      const Color(0xFFFFEB3B),
      const Color(0xFFFFC107),
      const Color(0xFFFF9800),
      const Color(0xFFFF5722),
    ];
    
    return List.generate(
      min(data.length, colors.length),
      (i) {
        final percentage = data[i].value / total;
        final value = data[i].value;
        final color = colors[i % colors.length];
        
        // Scale the section radius based on animation
        final radius = 60.0 * _animationController.value;
        
        return PieChartSectionData(
          value: value,
          title: '${(percentage * 100).toStringAsFixed(1)}%',
          titleStyle: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
          radius: radius,
          color: color,
        );
      },
    );
  }

  Widget _buildCategoryLegendItem(String categoryName, double amount, TransactionProvider provider) {
    final formattedAmount = provider.formatCurrency(amount);
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: _getCategoryColor(categoryName),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              categoryName,
              style: theme.textTheme.bodyLarge,
            ),
          ),
          Text(
            formattedAmount,
            style: theme.textTheme.bodyLarge!.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String categoryName) {
    final colors = {
      'Food': const Color(0xFF3F51B5),
      'Transport': const Color(0xFF2196F3),
      'Shopping': const Color(0xFFFF5722),
      'Utilities': const Color(0xFF4CAF50),
      'Entertainment': const Color(0xFF9C27B0),
      'Health': const Color(0xFF009688),
      'Salary': const Color(0xFF4CAF50),
      'Gift': const Color(0xFF9C27B0),
      'Interest': const Color(0xFF2196F3),
      'Loan': const Color(0xFFFF9800),
    };
    
    return colors[categoryName] ?? const Color(0xFF607D8B);
  }

  Widget _buildTransactionList(List<Transaction> transactions, TransactionProvider provider) {
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No transactions for selected period',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.withOpacity(0.8),
              ),
            ),
          ],
        ),
      );
    }
    
    // Group transactions by date
    final groupedTransactions = <DateTime, List<Transaction>>{};
    for (final transaction in transactions) {
      final date = DateTime(transaction.date.year, transaction.date.month, transaction.date.day);
      if (groupedTransactions.containsKey(date)) {
        groupedTransactions[date]!.add(transaction);
      } else {
        groupedTransactions[date] = [transaction];
      }
    }
    
    // Sort dates in descending order
    final sortedDates = groupedTransactions.keys.toList()
      ..sort((a, b) => b.compareTo(a));
    
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: sortedDates.length,
      itemBuilder: (context, dateIndex) {
        final date = sortedDates[dateIndex];
        final dayTransactions = groupedTransactions[date]!;
        
        // Sort transactions by time for each day
        dayTransactions.sort((a, b) => b.date.compareTo(a.date));
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                _formatDate(date),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            ...dayTransactions.map((transaction) => _buildTransactionItem(transaction, provider)),
          ],
        );
      },
    );
  }

  Widget _buildTransactionItem(Transaction transaction, TransactionProvider provider) {
    final theme = Theme.of(context);
    final category = provider.getCategoryById(transaction.categoryId);
    final formattedAmount = provider.formatCurrency(transaction.amount);
    
    Color amountColor;
    switch (transaction.type) {
      case TransactionType.expense:
        amountColor = Colors.red;
        break;
      case TransactionType.income:
        amountColor = Colors.green;
        break;
      case TransactionType.loan:
        amountColor = Colors.orange;
        break;
    }

    return SlideTransition(
      position: Tween<Offset>(begin: const Offset(-0.5, 0), end: Offset.zero).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeOutQuad,
        ),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _showTransactionDetails(transaction),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Category icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(category?.colorValue ?? 0xFF9E9E9E).withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      category?.icon ?? '📝',
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Transaction details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category?.name ?? 'Other',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (transaction.description.isNotEmpty)
                        Text(
                          transaction.description,
                          style: TextStyle(
                            color: theme.brightness == Brightness.light 
                                ? Colors.black54 
                                : Colors.grey.shade300,
                            fontSize: 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
                
                // Amount
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      transaction.type == TransactionType.expense 
                          ? '-$formattedAmount' 
                          : formattedAmount,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: amountColor,
                      ),
                    ),
                    Text(
                      DateFormat('h:mm a').format(transaction.date),
                      style: TextStyle(
                        fontSize: 12,
                        color: theme.brightness == Brightness.light 
                            ? Colors.black54 
                            : Colors.grey.shade300,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showTransactionDetails(Transaction transaction) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final category = provider.getCategoryById(transaction.categoryId);
    final theme = Theme.of(context);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Transaction Details',
                style: theme.textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Color(category?.colorValue ?? 0xFF9E9E9E).withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        category?.icon ?? '📝',
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category?.name ?? 'Other',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        Text(
                          transaction.description,
                          style: TextStyle(
                            color: theme.brightness == Brightness.light 
                                ? Colors.black54 
                                : Colors.grey.shade300,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Amount',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    provider.formatCurrency(transaction.amount),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: _getTransactionTypeColor(transaction.type),
                    ),
                  ),
                ],
              ),
              const Divider(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Date',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    DateFormat('MMMM d, yyyy').format(transaction.date),
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Time',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    DateFormat('h:mm a').format(transaction.date),
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Type',
                    style: TextStyle(
                      color: theme.brightness == Brightness.light 
                          ? Colors.black54 
                          : Colors.grey.shade300,
                    ),
                  ),
                  Text(
                    transaction.type.name.toUpperCase(),
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: _getTransactionTypeColor(transaction.type),
                    ),
                  ),
                ],
              ),
              if (transaction.tags.isNotEmpty) ...[              
                const SizedBox(height: 24),
                Text(
                  'Tags',
                  style: TextStyle(
                    color: theme.brightness == Brightness.light 
                        ? Colors.black54 
                        : Colors.grey.shade300,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: transaction.tags.map((tag) {
                    return Chip(
                      label: Text('#$tag'),
                      backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                      side: BorderSide(
                        color: theme.colorScheme.primary.withOpacity(0.2),
                      ),
                      padding: EdgeInsets.zero,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    );
                  }).toList(),
                ),
              ],
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () async {
                        // Close the modal
                        Navigator.of(context).pop();
                        
                        // Show edit dialog
                        final result = await showDialog<Transaction>(
                          context: context,
                          builder: (context) => TransactionEditDialog(transaction: transaction),
                        );
                        
                        if (result != null) {
                          // Update the transaction
                          await provider.updateTransaction(result);
                          
                          // Show success snackbar
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Transaction updated'),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('Edit'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        provider.deleteTransaction(transaction.id);
                        Navigator.of(context).pop();
                      },
                      icon: const Icon(Icons.delete),
                      label: const Text('Delete'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.income:
        return Colors.green;
      case TransactionType.loan:
        return Colors.orange;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    
    if (date == today) {
      return 'Today';
    } else if (date == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('EEEE, MMMM d').format(date);
    }
  }
}

// Helper function for min
int min(int a, int b) => a < b ? a : b;