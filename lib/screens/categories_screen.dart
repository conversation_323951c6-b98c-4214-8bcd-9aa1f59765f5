import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../models/transaction_model.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final Uuid _uuid = Uuid();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Categories'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'EXPENSE'),
            Tab(text: 'INCOME'),
            Tab(text: 'LOAN'),
          ],
          indicatorColor: theme.colorScheme.primary,
          labelColor: theme.brightness == Brightness.light 
              ? theme.colorScheme.primary 
              : Colors.white,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoryList(TransactionType.expense),
          _buildCategoryList(TransactionType.income),
          _buildCategoryList(TransactionType.loan),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCategoryDialog(),
        child: const Icon(Icons.add),
        tooltip: 'Add Category',
      ),
    );
  }

  Widget _buildCategoryList(TransactionType type) {
    return Consumer<TransactionProvider>(
      builder: (context, provider, child) {
        final categories = provider.categories
            .where((category) => category.type == type)
            .toList();
        
        if (categories.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.category_outlined,
                  size: 64,
                  color: Colors.grey.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No ${type.name} categories yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: () => _showCategoryDialog(type: type),
                  icon: const Icon(Icons.add),
                  label: Text('Add ${type.name} category'),
                ),
              ],
            ),
          );
        }
        
        return AnimatedList(
          key: GlobalKey<AnimatedListState>(),
          initialItemCount: categories.length,
          padding: const EdgeInsets.all(16),
          itemBuilder: (context, index, animation) {
            final category = categories[index];
            return SlideTransition(
              position: Tween<Offset>(begin: const Offset(1, 0), end: Offset.zero).animate(
                CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeOutQuint,
                ),
              ),
              child: FadeTransition(
                opacity: animation,
                child: Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Color(category.colorValue).withOpacity(0.2),
                      child: Text(
                        category.icon,
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                    title: Text(category.name),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () => _showCategoryDialog(category: category),
                          tooltip: 'Edit',
                        ),
                        // Only show delete button if it's not a default category
                        if (!_isDefaultCategory(category.id))
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _deleteCategory(category),
                            tooltip: 'Delete',
                          ),
                      ],
                    ),
                    onTap: () => _showCategoryDialog(category: category),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showCategoryDialog({Category? category, TransactionType? type}) {
    // If category is provided, we're editing, otherwise we're creating a new one
    final isEditing = category != null;
    
    // Initial values
    final initialType = category?.type ?? type ?? TransactionType.expense;
    final initialName = category?.name ?? '';
    final initialIcon = category?.icon ?? '\ud83d\udcb0';
    final initialColor = category != null ? Color(category.colorValue) : Colors.blue;
    
    // Form controllers
    final nameController = TextEditingController(text: initialName);
    
    // State management
    TransactionType selectedType = initialType;
    String selectedIcon = initialIcon;
    Color selectedColor = initialColor;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(isEditing ? 'Edit Category' : 'New Category'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Type selector
                  const Text('Type', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  SegmentedButton<TransactionType>(
                    segments: const [
                      ButtonSegment(
                        value: TransactionType.expense, 
                        label: Text('Expense'),
                        icon: Icon(Icons.arrow_upward),
                      ),
                      ButtonSegment(
                        value: TransactionType.income, 
                        label: Text('Income'),
                        icon: Icon(Icons.arrow_downward),
                      ),
                      ButtonSegment(
                        value: TransactionType.loan,
                        label: Text('Loan'),
                        icon: Icon(Icons.compare_arrows),
                      ),
                    ],
                    selected: {selectedType},
                    onSelectionChanged: (Set<TransactionType> selected) {
                      setState(() {
                        selectedType = selected.first;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Name field
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(),
                    ),
                    textCapitalization: TextCapitalization.words,
                  ),
                  const SizedBox(height: 16),
                  
                  // Icon selector
                  const Text('Icon', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildIconSelector(selectedIcon, (icon) {
                    setState(() {
                      selectedIcon = icon;
                    });
                  }),
                  const SizedBox(height: 16),
                  
                  // Color selector
                  const Text('Color', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  _buildColorSelector(selectedColor, (color) {
                    setState(() {
                      selectedColor = color;
                    });
                  }),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  final name = nameController.text.trim();
                  if (name.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please enter a category name')),
                    );
                    return;
                  }
                  
                  if (isEditing) {
                    _updateCategory(
                      category!.copyWith(
                        name: name,
                        icon: selectedIcon,
                        colorValue: selectedColor.value,
                        type: selectedType,
                      ),
                    );
                  } else {
                    _addCategory(
                      id: _generateCategoryId(name),
                      name: name,
                      icon: selectedIcon,
                      colorValue: selectedColor.value,
                      type: selectedType,
                    );
                  }
                  
                  Navigator.of(context).pop();
                },
                child: Text(isEditing ? 'UPDATE' : 'CREATE'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildIconSelector(String selectedIcon, Function(String) onIconSelected) {
    final icons = [
      '\ud83d\udcb0', '\ud83d\udcb5', '\ud83d\udcb3', '\ud83c\udf54', '\ud83d\ude97', 
      '\ud83d\udecd\ufe0f', '\ud83d\udca1', '\ud83c\udfac', '\ud83c\udfe5', '\ud83c\udf81', 
      '\ud83c\udfe6', '\ud83d\udcaa', '\ud83d\udcbb', '\ud83d\udcf1', '\ud83d\udc55', 
      '\ud83d\udc57', '\ud83d\udc5f', '\ud83c\udfd0', '\ud83c\udfcb\ufe0f', '\ud83d\udeba',
      '\ud83d\udcc8', '\ud83c\udf89', '\ud83d\udcb2', '\ud83d\udd04', '\ud83d\udcb8',
    ];
    
    return Container(
      height: 120,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: GridView.builder(
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 5,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
        ),
        itemCount: icons.length,
        itemBuilder: (context, index) {
          final icon = icons[index];
          final isSelected = icon == selectedIcon;
          
          return Material(
            color: isSelected ? Theme.of(context).colorScheme.primary.withOpacity(0.2) : Colors.transparent,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: () => onIconSelected(icon),
              child: Center(
                child: Text(
                  icon,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildColorSelector(Color selectedColor, Function(Color) onColorSelected) {
    final colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
    ];
    
    return Container(
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.all(8),
        itemCount: colors.length,
        itemBuilder: (context, index) {
          final color = colors[index];
          final isSelected = color.value == selectedColor.value;
          
          return GestureDetector(
            onTap: () => onColorSelected(color),
            child: Container(
              width: 34,
              height: 34,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: isSelected
                    ? Border.all(color: Colors.white, width: 2)
                    : null,
                boxShadow: isSelected
                    ? [BoxShadow(color: color.withOpacity(0.5), blurRadius: 8)]
                    : null,
              ),
              child: isSelected 
                  ? const Icon(Icons.check, color: Colors.white, size: 18) 
                  : null,
            ),
          );
        },
      ),
    );
  }

  void _addCategory({
    required String id,
    required String name,
    required String icon,
    required int colorValue,
    required TransactionType type,
  }) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    final newCategory = Category(
      id: id,
      name: name,
      icon: icon,
      colorValue: colorValue,
      type: type,
    );
    
    provider.addCategory(newCategory);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Category "$name" created')),
    );
  }

  void _updateCategory(Category updatedCategory) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    provider.updateCategory(updatedCategory);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Category "${updatedCategory.name}" updated')),
    );
  }

  void _deleteCategory(Category category) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text(
          'Are you sure you want to delete "${category.name}"? ' 
          'Any transactions using this category will be affected.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              provider.deleteCategory(category.id);
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Category "${category.name}" deleted')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

  String _generateCategoryId(String name) {
    // Generate a unique ID based on the name and a random UUID
    return '${name.toLowerCase().replaceAll(' ', '_')}_${_uuid.v4().substring(0, 8)}';
  }

  bool _isDefaultCategory(String id) {
    // Default category IDs list to prevent deletion
    final defaultCategoryIds = [
      'food', 'transport', 'shopping', 'utilities', 'entertainment', 'health',
      'salary', 'gift', 'investment', 'bonus', 'sales', 'crypto', 'refund', 'other_income',
      'loan', 'gift_expense',
    ];
    
    return defaultCategoryIds.contains(id);
  }
}

// Helper to create a copy of Category with updated fields
extension CategoryExtension on Category {
  Category copyWith({
    String? id,
    String? name,
    String? icon,
    int? colorValue,
    TransactionType? type,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      colorValue: colorValue ?? this.colorValue,
      type: type ?? this.type,
    );
  }
}