import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:intl/intl.dart';

import '../services/storage_service.dart';

enum TransactionType { expense, income, loan }

class Category {
  final String id;
  final String name;
  final String icon;
  final int colorValue;
  final TransactionType type;

  Category({
    required this.id,
    required this.name,
    required this.icon,
    required this.colorValue,
    required this.type,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'],
      name: json['name'],
      icon: json['icon'],
      colorValue: json['colorValue'],
      type: TransactionType.values.byName(json['type']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'colorValue': colorValue,
      'type': type.name,
    };
  }
}

class Transaction {
  final String id;
  final double amount;
  final TransactionType type;
  final String categoryId;
  final DateTime date;
  final String description;
  final List<String> tags;

  Transaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.categoryId,
    required this.date,
    required this.description,
    this.tags = const [],
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      amount: json['amount'].toDouble(),
      type: TransactionType.values.byName(json['type']),
      categoryId: json['categoryId'],
      date: DateTime.parse(json['date']),
      description: json['description'],
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'type': type.name,
      'categoryId': categoryId,
      'date': date.toIso8601String(),
      'description': description,
      'tags': tags,
    };
  }

  // Create a copy of the transaction with updated fields
  Transaction copyWith({
    String? id,
    double? amount,
    TransactionType? type,
    String? categoryId,
    DateTime? date,
    String? description,
    List<String>? tags,
  }) {
    return Transaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      categoryId: categoryId ?? this.categoryId,
      date: date ?? this.date,
      description: description ?? this.description,
      tags: tags ?? this.tags,
    );
  }
}

class ChatMessage {
  final String id;
  final String text;
  final DateTime timestamp;
  final bool isUserMessage;
  final String? associatedTransactionId;

  ChatMessage({
    required this.id,
    required this.text,
    required this.timestamp,
    required this.isUserMessage,
    this.associatedTransactionId,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      text: json['text'],
      timestamp: DateTime.parse(json['timestamp']),
      isUserMessage: json['isUserMessage'],
      associatedTransactionId: json['associatedTransactionId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'timestamp': timestamp.toIso8601String(),
      'isUserMessage': isUserMessage,
      'associatedTransactionId': associatedTransactionId,
    };
  }
}

class TransactionProvider with ChangeNotifier {
  final StorageService _storageService;
  List<Transaction> _transactions = [];
  List<Category> _categories = [];
  List<ChatMessage> _messages = [];
  
  // For pagination
  bool _isLoading = false;
  bool _hasMoreMessages = true;
  final int _messagesPerPage = 20;

  // Default categories
  final List<Category> _defaultCategories = [
    // Expense categories
    Category(id: 'food', name: 'Food', icon: '🍔', colorValue: 0xFFFF9800, type: TransactionType.expense),
    Category(id: 'transport', name: 'Transport', icon: '🚗', colorValue: 0xFF2196F3, type: TransactionType.expense),
    Category(id: 'shopping', name: 'Shopping', icon: '🛍️', colorValue: 0xFFF44336, type: TransactionType.expense),
    Category(id: 'utilities', name: 'Utilities', icon: '💡', colorValue: 0xFF4CAF50, type: TransactionType.expense),
    Category(id: 'entertainment', name: 'Entertainment', icon: '🎬', colorValue: 0xFF9C27B0, type: TransactionType.expense),
    Category(id: 'health', name: 'Health', icon: '🏥', colorValue: 0xFF009688, type: TransactionType.expense),
    Category(id: 'gift_expense', name: 'Gifts', icon: '🎁', colorValue: 0xFFE91E63, type: TransactionType.expense),
    
    // Income categories
    Category(id: 'salary', name: 'Salary', icon: '💰', colorValue: 0xFF4CAF50, type: TransactionType.income),
    Category(id: 'gift', name: 'Gift', icon: '🎁', colorValue: 0xFF9C27B0, type: TransactionType.income),
    Category(id: 'investment', name: 'Investment', icon: '📈', colorValue: 0xFF2196F3, type: TransactionType.income),
    Category(id: 'bonus', name: 'Bonus', icon: '🎉', colorValue: 0xFFFFEB3B, type: TransactionType.income),
    Category(id: 'sales', name: 'Sales', icon: '💳', colorValue: 0xFF3F51B5, type: TransactionType.income),
    Category(id: 'crypto', name: 'Crypto', icon: '💲', colorValue: 0xFFFF5722, type: TransactionType.income),
    Category(id: 'refund', name: 'Refund', icon: '🔄', colorValue: 0xFF795548, type: TransactionType.income),
    Category(id: 'other_income', name: 'Other Income', icon: '💸', colorValue: 0xFF607D8B, type: TransactionType.income),
    
    // Loan category
    Category(id: 'loan', name: 'Loan', icon: '🏦', colorValue: 0xFFFF9800, type: TransactionType.loan),
  ];

  TransactionProvider(this._storageService) {
    _loadData();
  }

  List<Transaction> get transactions => _transactions;
  List<Category> get categories => _categories;
  List<ChatMessage> get messages => _messages;
  bool get isLoading => _isLoading;
  bool get hasMoreMessages => _hasMoreMessages;

  // Get transactions by type
  List<Transaction> getTransactionsByType(TransactionType type) {
    return _transactions.where((transaction) => transaction.type == type).toList();
  }

  // Get transactions within date range
  List<Transaction> getTransactionsInDateRange(DateTime startDate, DateTime endDate) {
    return _transactions.where((transaction) => 
      transaction.date.isAfter(startDate.subtract(const Duration(days: 1))) && 
      transaction.date.isBefore(endDate.add(const Duration(days: 1)))
    ).toList();
  }

  // Get category by ID
  Category? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  // Add a new transaction
  Future<void> addTransaction(Transaction transaction) async {
    _transactions.add(transaction);
    await _saveTransactions();
    notifyListeners();
  }

  // Add a transaction directly from chat (no confirmation)
  Future<void> addTransactionFromChat(Transaction transaction, String userMessage) async {
    // First add the transaction
    await addTransaction(transaction);
    
    // Then generate response message
    final category = getCategoryById(transaction.categoryId);
    final categoryName = category?.name ?? 'Other';
    
    // Add system response with the transaction details
    final message = ChatMessage(
      id: transaction.id,
      text: '✅ Transaction saved: ${formatCurrency(transaction.amount)} for ${transaction.description}, Category: $categoryName',
      timestamp: DateTime.now(),
      isUserMessage: false,
      associatedTransactionId: transaction.id,
    );
    
    // Make sure to await this so the UI can update accordingly
    await addMessage(message);
  }

  // Update an existing transaction
  Future<void> updateTransaction(Transaction updatedTransaction) async {
    final index = _transactions.indexWhere((t) => t.id == updatedTransaction.id);
    if (index != -1) {
      _transactions[index] = updatedTransaction;
      await _saveTransactions();
      notifyListeners();
    }
  }

  // Delete a transaction
  Future<void> deleteTransaction(String id) async {
    _transactions.removeWhere((transaction) => transaction.id == id);
    
    // Also remove any associated chat messages
    _messages.removeWhere((message) => message.associatedTransactionId == id);
    
    await _saveTransactions();
    await _saveMessages();
    notifyListeners();
  }

  // Add a new category
  Future<void> addCategory(Category category) async {
    _categories.add(category);
    await _saveCategories();
    notifyListeners();
  }

  // Update an existing category
  Future<void> updateCategory(Category updatedCategory) async {
    final index = _categories.indexWhere((c) => c.id == updatedCategory.id);
    if (index != -1) {
      _categories[index] = updatedCategory;
      await _saveCategories();
      notifyListeners();
    }
  }

  // Delete a category
  Future<void> deleteCategory(String id) async {
    _categories.removeWhere((category) => category.id == id);
    await _saveCategories();
    notifyListeners();
  }

  // Add a new chat message
  Future<void> addMessage(ChatMessage message) async {
    _messages.add(message);
    await _saveMessages();
    notifyListeners();
  }
  
  // Load more messages (for pagination)
  Future<void> loadMoreMessages() async {
    if (_isLoading || !_hasMoreMessages) return;
    
    _isLoading = true;
    notifyListeners();
    
    await Future.delayed(const Duration(milliseconds: 500)); // simulate loading
    
    // Load all messages from storage
    final allMessages = await _getAllMessagesFromStorage();
    if (allMessages.isEmpty) {
      _isLoading = false;
      _hasMoreMessages = false;
      notifyListeners();
      return;
    }
    
    // Sort by timestamp (oldest to newest)
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // Find the oldest message we currently have
    final oldestTimestamp = _messages.isEmpty ? DateTime.now() : _messages.first.timestamp;
    
    // Get messages older than our oldest message
    final olderMessages = allMessages.where((m) => m.timestamp.isBefore(oldestTimestamp)).toList();
    
    if (olderMessages.isEmpty) {
      _hasMoreMessages = false;
    } else {
      // Take up to messagesPerPage older messages
      final messagesToAdd = olderMessages.length > _messagesPerPage 
          ? olderMessages.sublist(olderMessages.length - _messagesPerPage) 
          : olderMessages;
      
      // Insert at the beginning (older messages)
      _messages.insertAll(0, messagesToAdd);
      
      // If we got all remaining messages, there are no more to load
      _hasMoreMessages = olderMessages.length > messagesToAdd.length;
    }
    
    _isLoading = false;
    notifyListeners();
  }

  // Load all data from storage
  Future<void> _loadData() async {
    await _loadTransactions();
    await _loadCategories();
    await _loadInitialMessages();
  }
  
  // Load initial batch of messages
  Future<void> _loadInitialMessages() async {
    final allMessages = await _getAllMessagesFromStorage();
    
    if (allMessages.isEmpty) {
      _hasMoreMessages = false;
      return;
    }
    
    // Sort by timestamp (oldest to newest)
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    // Take only the most recent messages
    final totalMessages = allMessages.length;
    final startIndex = totalMessages > _messagesPerPage ? totalMessages - _messagesPerPage : 0;
    _messages = allMessages.sublist(startIndex, totalMessages);
    
    // If we loaded from the beginning, there are no more older messages to load
    _hasMoreMessages = startIndex > 0;
  }
  
  // Load all messages from storage without pagination
  Future<void> _loadAllMessages() async {
    _messages = await _getAllMessagesFromStorage();
    _hasMoreMessages = false;
  }
  
  // Get all messages from storage
  Future<List<ChatMessage>> _getAllMessagesFromStorage() async {
    final messagesJson = await _storageService.getStringList('messages');
    if (messagesJson != null && messagesJson.isNotEmpty) {
      return messagesJson
          .map((json) => ChatMessage.fromJson(jsonDecode(json)))
          .toList();
    }
    return [];
  }

  // Load transactions from storage
  Future<void> _loadTransactions() async {
    final transactionsJson = await _storageService.getStringList('transactions');
    if (transactionsJson != null) {
      _transactions = transactionsJson
          .map((json) => Transaction.fromJson(jsonDecode(json)))
          .toList();
    }
  }

  // Save transactions to storage
  Future<void> _saveTransactions() async {
    final transactionsJson = _transactions
        .map((transaction) => jsonEncode(transaction.toJson()))
        .toList();
    await _storageService.setStringList('transactions', transactionsJson);
  }

  // Load categories from storage
  Future<void> _loadCategories() async {
    final categoriesJson = await _storageService.getStringList('categories');
    if (categoriesJson != null && categoriesJson.isNotEmpty) {
      _categories = categoriesJson
          .map((json) => Category.fromJson(jsonDecode(json)))
          .toList();
    } else {
      // Use default categories if none are saved
      _categories = _defaultCategories;
      await _saveCategories();
    }
  }

  // Save categories to storage
  Future<void> _saveCategories() async {
    final categoriesJson = _categories
        .map((category) => jsonEncode(category.toJson()))
        .toList();
    await _storageService.setStringList('categories', categoriesJson);
  }

  // Save messages to storage
  Future<void> _saveMessages() async {
    final messagesJson = _messages
        .map((message) => jsonEncode(message.toJson()))
        .toList();
    await _storageService.setStringList('messages', messagesJson);
  }

  // Get total for a specific transaction type within date range
  double getTotalByTypeInRange(TransactionType type, DateTime startDate, DateTime endDate) {
    return getTransactionsInDateRange(startDate, endDate)
        .where((t) => t.type == type)
        .fold(0, (sum, transaction) => sum + transaction.amount);
  }

  // Get spending by category within date range
  Map<String, double> getSpendingByCategory(DateTime startDate, DateTime endDate) {
    final Map<String, double> result = {};
    
    final filteredTransactions = getTransactionsInDateRange(startDate, endDate)
        .where((t) => t.type == TransactionType.expense);
    
    for (var transaction in filteredTransactions) {
      if (result.containsKey(transaction.categoryId)) {
        result[transaction.categoryId] = result[transaction.categoryId]! + transaction.amount;
      } else {
        result[transaction.categoryId] = transaction.amount;
      }
    }
    
    return result;
  }

  // Format currency amount
  String formatCurrency(double amount) {
    return NumberFormat.currency(symbol: "\$", decimalDigits: 2).format(amount);
  }
  
  // Clear all data
  Future<void> clearAllData() async {
    // Clear all transactions
    _transactions.clear();
    await _saveTransactions();
    
    // Clear all messages
    _messages.clear();
    await _saveMessages();
    
    // Reset categories to defaults
    _categories = _defaultCategories;
    await _saveCategories();
    
    notifyListeners();
  }
}