import 'package:flutter/material.dart';
import '../screens/chat_screen.dart';
import '../screens/statistics_screen.dart';
import '../screens/settings_screen.dart';

class AppNavigation extends StatefulWidget {
  const AppNavigation({super.key});

  @override
  State<AppNavigation> createState() => _AppNavigationState();
}

class _AppNavigationState extends State<AppNavigation> {
  int _currentIndex = 0;
  
  // Create screens only once to preserve their state
  final List<Widget> _screens = const [
    ChatScreen(),
    StatisticsScreen(),
    SettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    // Get the color scheme from the current theme
    final colorScheme = Theme.of(context).colorScheme;
    
    // Use the color scheme to define the navigation bar colors
    final navigationBarTheme = NavigationBarThemeData(
      indicatorColor: colorScheme.secondaryContainer,
      labelTextStyle: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return TextStyle(color: colorScheme.onSecondaryContainer, fontSize: 12);
        }
        return TextStyle(color: colorScheme.onSurfaceVariant, fontSize: 12);
      }),
    );
    
    return Scaffold(
      // Use IndexedStack instead of directly showing the screen
      // This keeps all screens in memory and preserves their state
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          navigationBarTheme: navigationBarTheme,
        ),
        child: NavigationBar(
          selectedIndex: _currentIndex,
          onDestinationSelected: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
          animationDuration: const Duration(milliseconds: 500),
          destinations: const [
            NavigationDestination(
              icon: Icon(Icons.chat_bubble_outline),
              selectedIcon: Icon(Icons.chat_bubble),
              label: 'Chat',
            ),
            NavigationDestination(
              icon: Icon(Icons.bar_chart_outlined),
              selectedIcon: Icon(Icons.bar_chart),
              label: 'Statistics',
            ),
            NavigationDestination(
              icon: Icon(Icons.settings_outlined),
              selectedIcon: Icon(Icons.settings),
              label: 'Settings',
            ),
          ],
        ),
      ),
    );
  }
}