import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'models/transaction_model.dart';
import 'services/storage_service.dart';
import 'navigation/app_navigation.dart';
import 'theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final storageService = StorageService();
  await storageService.init();
  
  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ChangeNotifierProvider(create: (_) => TransactionProvider(storageService)),
    ],
    child: const MoneyLoverChatApp(),
  ));
}

class MoneyLoverChatApp extends StatelessWidget {
  const MoneyLoverChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return MaterialApp(
      title: 'Money Lover Chat',
      theme: themeProvider.themeData,
      debugShowCheckedModeBanner: false,
      home: const AppNavigation(),
    );
  }
}